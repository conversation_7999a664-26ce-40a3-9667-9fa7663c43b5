import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { TodoistApi } from '@doist/todoist-api-typescript';
import { Command, Option } from 'commander';
import { z } from 'zod';

type CliOptions = {
  todoistToken: string;
}

// Parse command line arguments
const program = new Command();
program
  .name('home-mcp')
  .description('MCP server with Todoist integration')
  .version('0.1.0')
  .addOption(new Option('--todoist-token <token>', 'Todoist API token')
    .env('TODOIST_API_TOKEN')
    .makeOptionMandatory(true))
  .parse();

const { todoistToken } = program.opts<CliOptions>();

// Get API token from CLI argument or environment variable

if (!todoistToken || typeof todoistToken !== 'string') {
  console.error('Error: Todoist API token is required. Provide it via --token argument or TODOIST_API_TOKEN environment variable.');
  process.exit(1);
}

// Initialize Todoist API client
const todoist = new TodoistApi(todoistToken);

// Create an MCP server
const server = new McpServer({
  name: 'home-mcp',
  version: '0.1.0',
});

// Add a simple ping tool that responds with "pong"
server.tool(
  'ping',
  {}, // No parameters needed for ping
  () => ({
    content: [{ type: 'text', text: 'pong' }],
  })
);

// Add Todoist tools
server.tool(
  'list_projects',
  {}, // No parameters needed
  async () => {
    try {
      const projects = await todoist.getProjects();
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(projects, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching projects: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'list_tasks',
  {
    projectId: z.string().describe('The ID of the project to list tasks from'),
  },
  async ({ projectId }) => {
    try {
      const tasks = await todoist.getTasks({ projectId });
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(tasks, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching tasks for project ${projectId}: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'create_task',
  {
    content: z.string().describe('The content/title of the task'),
    projectId: z.string().describe('The ID of the project to create the task in'),
    description: z.string().optional().describe('Optional description for the task'),
    dueString: z.string().optional().describe('Optional due date in natural language (e.g., "tomorrow", "next Monday")'),
    priority: z.number().min(1).max(4).optional().describe('Task priority (1-4, where 4 is highest priority)'),
  },
  async ({ content, projectId, description, dueString, priority }) => {
    try {
      const task = await todoist.addTask({
        content,
        projectId,
        description,
        dueString,
        priority,
      });
      return {
        content: [
          {
            type: 'text',
            text: `Task created successfully:\n${JSON.stringify(task, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error creating task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

// Start the server with stdio transport
const transport = new StdioServerTransport();
await server.connect(transport);
